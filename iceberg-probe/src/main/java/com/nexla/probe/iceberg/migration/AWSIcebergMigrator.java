package com.nexla.probe.iceberg.migration;

import com.google.common.annotations.VisibleForTesting;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.security.UserGroupInformation;
import org.apache.spark.sql.SparkSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import scala.Option;

import java.util.concurrent.CompletableFuture;

/**
 * Manages AWS credential handling and delegates to AuroraSnapshotToIcebergMigrator
 * for the actual migration process.
 */
public class AWSIcebergMigrator {
  private static final Logger log = LoggerFactory.getLogger(AWSIcebergMigrator.class);

  // =================================================================================
  // === DEFINITIVE FIX: Static initializer block.                                 ===
  // === This code runs once when the class is loaded by the JVM, ensuring the     ===
  // === properties are set before any Spark/Hadoop code is ever executed.         ===
  // =================================================================================
  static {
    try {
      Configuration conf = new Configuration();
      conf.set("hadoop.security.authentication", "simple");
      UserGroupInformation.setConfiguration(conf);
      UserGroupInformation.setLoginUser(UserGroupInformation.createRemoteUser("nexla"));
    } catch (Exception e) {
      log.error("Error setting up Hadoop user context.", e);
    }
    log.info("Static Initializer: Setting Hadoop user context for containerized environment.");
    System.setProperty("user.name", "nexla");
    System.setProperty("hadoop.security.authentication", "simple");
    System.setProperty("java.security.krb5.conf", "/dev/null");
  }

  private final CredentialsHelper credentialsHelper;
  private final RunIdLogger logger;

  public AWSIcebergMigrator(final String runId, final CredentialsHelper credentialsHelper) {
    this.logger = new RunIdLogger(runId, log);
    this.credentialsHelper = credentialsHelper;
  }

  /**
   * Migrates data from AWS S3 snapshot to Iceberg format.
   *
   * @param awsCredentialId             AWS credentials ID
   * @param sourceSnapshotPath          Source snapshot path
   * @param destinationIcebergWarehouse Destination Iceberg warehouse path
   */
  public void migrate(final int awsCredentialId,
                      final String sourceSnapshotPath,
                      final String destinationIcebergWarehouse) {
    logger.info("Starting AWS Iceberg migration with credential ID: {}, sourceSnapshotPath: {}, destinationIcebergWarehouse: {}",
        awsCredentialId, sourceSnapshotPath, destinationIcebergWarehouse);

    // Fetch AWS credentials using the specified ID
    final var credentials = credentialsHelper.enrichWithDataCredentials(awsCredentialId);
    final var catalogName = "spark_catalog";

    // Correctly handle scala.Option in Java to stop any existing SparkContext.
    Option<SparkSession> activeSessionOption = SparkSession.getActiveSession();
    if (activeSessionOption.isDefined()) {
        logger.warn("An active Spark session was found. Stopping it before creating a new one.");
        activeSessionOption.get().stop();
    }

    // Configure and create Spark session with AWS credentials
    final SparkSession spark = SparkSession.builder()
    .appName("Aurora Snapshot to Iceberg Migration")
    .config("spark.hadoop.user.name", "nexla")
    .config("spark.executorEnv.USER", "nexla")
    .config("spark.executorEnv.HADOOP_USER_NAME", "nexla")
    .config("spark.hadoop.hadoop.security.authentication", "simple")
    .config("spark.hadoop.hadoop.security.authorization", "false")
    .master("local[*]")
    .config("spark.hadoop.fs.s3a.impl", "org.apache.hadoop.fs.s3a.S3AFileSystem")
    .config("spark.hadoop.fs.s3a.path.style.access", "true")
    // Aggressive connection pool settings to prevent pool exhaustion
    .config("spark.hadoop.fs.s3a.connection.maximum", "1000")
    .config("spark.hadoop.fs.s3a.endpoint", "s3.amazonaws.com")
    .config("spark.sql.extensions", "org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions")
    .config("spark.sql.catalog.spark_catalog", "org.apache.iceberg.spark.SparkCatalog")
    .config("spark.sql.catalog.spark_catalog.type", "hadoop")
    .config("spark.sql.catalog.spark_catalog.handle-timestamp-without-timezone", "true")
    .config("spark.sql.parquet.datetimeRebaseModeInWrite", "CORRECTED")
    .config("spark.sql.catalog.spark_catalog.warehouse", destinationIcebergWarehouse)
    .config("spark.hadoop.fs.s3a.endpoint.region", credentials.getRegion())
    .config("spark.hadoop.fs.s3a.access.key", credentials.getAccessKeyId())
    .config("spark.hadoop.fs.s3a.secret.key", credentials.getSecretKey())
    // Ultra-aggressive timeout and retry configurations
    .config("spark.hadoop.fs.s3a.connection.timeout", "600000")
    .config("spark.hadoop.fs.s3a.socket.timeout", "600000")
    .config("spark.hadoop.fs.s3a.connection.establish.timeout", "120000")
    .config("spark.hadoop.fs.s3a.connection.acquisition.timeout", "600000")
    .config("spark.hadoop.fs.s3a.connection.request.timeout", "600000")
    .config("spark.hadoop.fs.s3a.connection.part.upload.timeout", "1800000")
    .config("spark.hadoop.fs.s3a.attempts.maximum", "20")
    .config("spark.hadoop.fs.s3a.retry.interval", "5000")
    .config("spark.hadoop.fs.s3a.retry.limit", "20")
    .config("spark.hadoop.fs.s3a.timeout", "1800000")
    .config("spark.hadoop.fs.s3a.connection.ttl", "900000")
    .config("spark.hadoop.fs.s3a.connection.idle.time", "600000")
    // Massive thread pool for high concurrency
    .config("spark.hadoop.fs.s3a.threads.max", "200")
    .config("spark.hadoop.fs.s3a.threads.core", "100")
    .config("spark.hadoop.fs.s3a.max.total.tasks", "1000")
    .config("spark.hadoop.fs.s3a.executor.capacity", "100")
    // Connection pool leak detection and management
    .config("spark.hadoop.fs.s3a.connection.pool.leak.detection.threshold", "60000")
    .config("spark.hadoop.fs.s3a.connection.pool.validation.query", "SELECT 1")
    .config("spark.hadoop.fs.s3a.connection.pool.validation.interval", "30000")
    .config("spark.hadoop.fs.s3a.connection.pool.eviction.run.interval", "30000")
    .config("spark.hadoop.fs.s3a.connection.pool.min.evictable.idle.time", "300000")
    .config("spark.hadoop.fs.s3a.connection.pool.max.idle", "200")
    .config("spark.hadoop.fs.s3a.connection.pool.min.idle", "50")
    // Enhanced multipart and upload configurations
    .config("spark.hadoop.fs.s3a.multipart.size", "268435456")
    .config("spark.hadoop.fs.s3a.multipart.threshold", "536870912")
    .config("spark.hadoop.fs.s3a.fast.upload", "true")
    .config("spark.hadoop.fs.s3a.fast.upload.buffer", "disk")
    .config("spark.hadoop.fs.s3a.fast.upload.active.blocks", "32")
    .config("spark.hadoop.fs.s3a.block.size", "268435456")
    // S3A performance and read optimizations
    .config("spark.hadoop.fs.s3a.list.version", "2")
    .config("spark.hadoop.fs.s3a.experimental.input.fadvise", "sequential")
    .config("spark.hadoop.fs.s3a.readahead.range", "2097152")
    .config("spark.hadoop.fs.s3a.vectored.read.min.seek.size", "8192")
    .config("spark.hadoop.fs.s3a.vectored.read.max.merged.size", "2097152")
    .config("spark.hadoop.fs.s3a.vectored.active.ranged.reads", "16")
    // Enhanced connection pool and retry optimizations
    .config("spark.hadoop.fs.s3a.retry.throttle.limit", "50")
    .config("spark.hadoop.fs.s3a.retry.throttle.interval", "2000")
    .config("spark.hadoop.fs.s3a.retry.backoff.exponential", "true")
    .config("spark.hadoop.fs.s3a.retry.backoff.max.delay", "30000")
    .config("spark.hadoop.fs.s3a.retry.backoff.base.delay", "1000")
    .config("spark.hadoop.fs.s3a.connection.keepalive", "true")
    .config("spark.hadoop.fs.s3a.connection.ssl.enabled", "true")
    .config("spark.hadoop.fs.s3a.ssl.channel.mode", "default_jsse")
    // Performance flags and optimizations
    .config("spark.hadoop.fs.s3a.create.performance", "true")
    .config("spark.hadoop.fs.s3a.performance.flag", "create")
    .config("spark.hadoop.fs.creation.parallel.count", "16")
    // AWS SDK specific configurations for connection pool management
    .config("spark.hadoop.fs.s3a.aws.credentials.provider", "org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider")
    .config("spark.hadoop.fs.s3a.connection.pool.enabled", "true")
    .config("spark.hadoop.fs.s3a.connection.pool.size", "1000")
    .config("spark.hadoop.fs.s3a.connection.pool.timeout", "600000")
    // HTTP client specific configurations
    .config("spark.hadoop.fs.s3a.http.socket.timeout", "600000")
    .config("spark.hadoop.fs.s3a.http.connection.timeout", "600000")
    .config("spark.hadoop.fs.s3a.http.connection.request.timeout", "600000")
    .config("spark.hadoop.fs.s3a.http.max.connections", "1000")
    .config("spark.hadoop.fs.s3a.http.max.connections.per.route", "200")
    //.config("spark.hadoop.fs.s3a.session.token", credentials.getSessionToken())
    .getOrCreate();

    // Additional S3 configurations - Remove duplicate and conflicting settings
    // These are now handled in the SparkSession builder above
    // spark.sparkContext().hadoopConfiguration().set("fs.s3a.connection.timeout", "5000");
    // spark.sparkContext().hadoopConfiguration().set("fs.s3a.attempts.maximum", "3");
    // spark.sparkContext().hadoopConfiguration().set("fs.s3a.experimental.input.fadvise", "sequential");
    CompletableFuture.runAsync(() -> {
      try {
        final var migrator = createMigrator(
            spark, sourceSnapshotPath, destinationIcebergWarehouse, catalogName);
        migrator.migrate();
        logger.info("Migration completed successfully");
      } catch (final Exception e) {
        logger.error("Migration failed: awsCredentialId: {}, sourceSnapshotPath: {}, destinationIcebergWarehouse: {}",
            awsCredentialId, sourceSnapshotPath, destinationIcebergWarehouse, e);
      } finally {
        spark.stop();
      }
    });
  }

  @VisibleForTesting
  AuroraSnapshotToIcebergMigrator createMigrator(
      final SparkSession spark,
      final String sourceSnapshotPath,
      final String destinationIcebergWarehouse,
      final String catalogName) {
    return new AuroraSnapshotToIcebergMigrator(logger, spark, sourceSnapshotPath, destinationIcebergWarehouse, catalogName);
  }
}